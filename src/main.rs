

struct Solution;
use std::collections::{HashMap, HashSet};
// use std::collections::HashMap;
// Definition for a binary tree node.
#[derive(Debug, PartialEq, Eq)]
pub struct TreeNode {
  pub val: i32,
  pub left: Option<Rc<RefCell<TreeNode>>>,
  pub right: Option<Rc<RefCell<TreeNode>>>,
}

impl TreeNode {
  #[inline]
  pub fn new(val: i32) -> Self {
    TreeNode {
      val,
      left: None,
      right: None
    }
  }
}
use std::rc::Rc;
use std::cell::RefCell;
// Definition for a binary tree node.
// #[derive(Debug, PartialEq, Eq)]
// pub struct TreeNode {
//   pub val: i32,
//   pub left: Option<Rc<RefCell<TreeNode>>>,
//   pub right: Option<Rc<RefCell<TreeNode>>>,
// }
//
// impl TreeNode {
//   #[inline]
//   pub fn new(val: i32) -> Self {
//     TreeNode {
//       val,
//       left: None,
//       right: None
//     }
//   }
// }
// use std::rc::Rc;
// use std::cell::RefCell;
// use std::collections::HashMap;
// use std::collections::HashSet;
impl Solution {
    pub fn assign_bikes(workers: Vec<Vec<i32>>, bikes: Vec<Vec<i32>>) -> Vec<i32> {
        let mut aw:Vec<i32> = Vec::new();
        let mut ab:Vec<i32> = Vec::new();
        let mut maxd = i32::MA;
        let mut minw = i32::Max;
        let mut minb = i32::Max;
        let mut r = Vec::new();
        while ab.len() < workers.len() {
            let mut bf = false;
            for i in 0..workers.len() {
                if aw.contains(i) {
                    continue;
                }
                for j in 0..bikes.len() {
                    if ab.contains(j) {
                        continue;
                    }
                    let d = (workers[i] - bikes[i]).abs() + (workers[j] - bikes[j]).abs();
                    if d == 2 {
                        aw.push(i);
                        ab.push(j);
                        r.push(j);
                        bf = true;
                        break;
                    }else{
                        if d <maxd {
                            maxd = d;
                            minw = i;
                            minb = j;
                        }
                    }
                }
                if bf {
                    break;
                }else{
                    r.push(minb);
                    aw.push(minw);
                    ab.push(minb);
                    break;
                }
            }
        }
        return r;
    }
}



fn main() {
    let a = Solution::assign_bikes(vec![vec![0,0],vec![2,1]], vec![vec![1,2],vec![3,3]]);
    println!("{:?}", a);
}
